# dashboard_application/modes/flow_mode_display_v2_5.py
# EOTS v2.5 - S-GRADE, AUTHORITATIVE FLOW MODE DISPLAY
# Enhanced with SGDHP, IVSDH, and UGCH Heatmaps

import pandas as pd
import plotly.graph_objects as go
from dash import dcc, html
import dash_bootstrap_components as dbc
import numpy as np
from typing import Optional
from dash.development.base_component import Component

from data_models import FinalAnalysisBundleV2_5 # Updated import
from utils.config_manager_v2_5 import ConfigManagerV2_5
from dashboard_application.utils_dashboard_v2_5 import (
    create_empty_figure, apply_dark_theme_template, 
    add_price_line, add_timestamp_annotation, PLOTLY_TEMPLATE
)
import logging

logger = logging.getLogger(__name__)

# --- Helper Functions for Chart Generation ---

# CRITICAL FIX: Import centralized chart styling utility to eliminate duplicates
from dashboard_application.utils.chart_styling_utils_v2_5 import (
    create_pydantic_graph_style, create_heatmap_style, CHART_CONTAINER_CLASSES
)

def _about_section(text, component_id):
    # Collapsible about section with toggle button
    return html.Div([
        dbc.Button(
            "About",
            id={"type": "about-toggle-btn", "section": component_id},
            color="info",
            size="sm",
            className="mb-2",
            n_clicks=0,
        ),
        dbc.Collapse(
            html.Div(text, className="about-section mb-2"),
            id={"type": "about-collapse", "section": component_id},
            is_open=False,
        ),
        # Note: Wire up a Dash callback to toggle is_open on button click for each card.
        # Example callback signature:
        # @app.callback(
        #   Output({"type": "about-collapse", "section": MATCH}, "is_open"),
        #   Input({"type": "about-toggle-btn", "section": MATCH}, "n_clicks"),
        #   State({"type": "about-collapse", "section": MATCH}, "is_open"),
        # )
        # def toggle_about(n, is_open):
        #     if n:
        #         return not is_open
        #     return is_open
    ], id=f"about-{component_id}", className="about-section-container mb-2")

def _wrap_chart_in_card(chart_component, about_text, component_id):
    return dbc.Card([
        dbc.CardHeader(html.H5(component_id.replace('-', ' ').title())),
        dbc.CardBody([
            _about_section(about_text, component_id),
            chart_component
        ])
    ], className="mb-4", id=f"card-{component_id}")

def _generate_net_value_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates a heatmap of net value pressure by strike and option type."""
    chart_name = "Net Value by Strike"
    # CRITICAL FIX: Enhanced configuration access using proper Pydantic v2 model
    try:
        dashboard_config = config.config.visualization_settings.dashboard
        flow_settings = getattr(dashboard_config, 'flow_mode_settings', None)
        if flow_settings and hasattr(flow_settings, 'net_value_heatmap'):
            default_fig_height_for_empty = flow_settings.net_value_heatmap.height
        else:
            default_fig_height_for_empty = 600
    except (AttributeError, KeyError):
        default_fig_height_for_empty = 600
    about_text = (
        "💡 Net Value by Strike: Visualizes the net value pressure (call/put) across strikes. "
        "Red = net selling/pressure, Green = net buying/support. "
        "Use this to spot where the largest value flows are concentrated and potential support/resistance zones."
    )
    
    try:
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if options_data is None:
            logger.error(f"[{chart_name}] options_data_with_metrics missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("Options data unavailable.", color="danger"), about_text, "net-value-heatmap")
        df_chain = pd.DataFrame([c.model_dump() for c in options_data])
        if df_chain.empty or 'value_bs' not in df_chain.columns:
            # CRITICAL FIX: Use centralized Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{default_fig_height_for_empty}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, default_fig_height_for_empty, "Per-contract 'value_bs' data not available."),
                config=error_config,
                style=error_style
            ), about_text, "net-value-heatmap")

        # CRITICAL FIX: Zero-tolerance fake data compliance - NO fillna(0) allowed
        df_plot = df_chain.dropna(subset=['strike', 'opt_kind', 'value_bs'])
        pivot_df = df_plot.pivot_table(index='strike', columns='opt_kind', values='value_bs', aggfunc='sum')

        # FAIL-FAST: Require real data for both call and put options
        if 'call' not in pivot_df.columns or 'put' not in pivot_df.columns:
            missing_types = []
            if 'call' not in pivot_df.columns: missing_types.append('call')
            if 'put' not in pivot_df.columns: missing_types.append('put')
            logger.error(f"[{chart_name}] Missing required option types: {missing_types}. Cannot display heatmap with incomplete data.")
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{default_fig_height_for_empty}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, default_fig_height_for_empty, f"Missing required option data: {', '.join(missing_types)}"),
                config=error_config,
                style=error_style,
                className=CHART_CONTAINER_CLASSES
            ), about_text, "net-value-heatmap")

        # Only use real data - no synthetic values
        pivot_df = pivot_df[['put', 'call']].sort_index(ascending=True)

        # FAIL-FAST: Check for any NaN values that would indicate missing real data
        if pivot_df.isna().any().any():
            nan_count = pivot_df.isna().sum().sum()
            logger.error(f"[{chart_name}] Found {nan_count} missing values in net value data. Cannot display heatmap with incomplete financial data.")
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{default_fig_height_for_empty}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, default_fig_height_for_empty, f"Incomplete net value data: {nan_count} missing values"),
                config=error_config,
                style=error_style
            ), about_text, "net-value-heatmap")
        
        if pivot_df.empty:
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{default_fig_height_for_empty}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, default_fig_height_for_empty, "No data to pivot."),
                config=error_config,
                style=error_style
            ), about_text, "net-value-heatmap")

        # Enhanced height calculation with proper configuration integration
        num_strikes = len(pivot_df.index.unique())

        # CRITICAL FIX: Access flow_mode_settings as proper Pydantic v2 model
        flow_settings = None  # Initialize to prevent UnboundLocalError
        try:
            dashboard_config = config.config.visualization_settings.dashboard
            flow_settings = getattr(dashboard_config, 'flow_mode_settings', None)

            # Use configuration settings with proper Pydantic v2 model access
            if flow_settings and hasattr(flow_settings, 'net_value_heatmap'):
                heatmap_config = flow_settings.net_value_heatmap
                base_height = heatmap_config.height
                autosize_enabled = heatmap_config.autosize
                responsive_enabled = heatmap_config.responsive
            else:
                base_height = 600
                autosize_enabled = False
                responsive_enabled = False
        except (AttributeError, KeyError):
            # Fallback to safe defaults
            flow_settings = None
            base_height = 600
            autosize_enabled = False
            responsive_enabled = False

        # CRITICAL FIX: Enhanced adaptive height calculation using proper Pydantic v2 model access
        if flow_settings and hasattr(flow_settings, 'net_value_heatmap'):
            heatmap_config = flow_settings.net_value_heatmap
            row_height_px = heatmap_config.row_height_px
            header_footer_px = heatmap_config.header_footer_px
            min_fig_height = heatmap_config.min_height
            max_fig_height = heatmap_config.max_height
            enhanced_scaling = heatmap_config.enhanced_scaling
        else:
            # Fallback values with improved defaults for horizontal heatmaps
            row_height_px = max(25, min(50, 800 // max(1, num_strikes)))
            header_footer_px = 200
            min_fig_height = 500
            max_fig_height = 1200
            enhanced_scaling = True

        # CRITICAL FIX: Container-aware height calculation to prevent cut-off
        # Calculate optimal height based on data volume and container constraints
        strikes_count = len(pivot_df.index) if not pivot_df.empty else 1

        # Enhanced height calculation with container awareness
        if autosize_enabled:
            # For autosize, use container-constrained height
            container_max_height = 600  # Conservative max for card containers
            optimal_height = min(base_height, container_max_height)
            dynamic_height = optimal_height
        else:
            # Calculate adaptive height with container constraints
            adaptive_height = (strikes_count * row_height_px) + header_footer_px

            # Apply container constraints to prevent cut-off
            container_max_height = 650  # Maximum height that fits in card containers
            container_constrained_height = min(adaptive_height, container_max_height)

            # Ensure within configured bounds
            dynamic_height = max(min_fig_height, min(container_constrained_height, max_fig_height))

        # CRITICAL FIX: Enhanced heatmap configuration for proper scaling and display
        fig = go.Figure(data=go.Heatmap(
            z=pivot_df.values,
            x=pivot_df.columns.str.capitalize(),
            y=pivot_df.index.astype(str),
            colorscale='RdYlGn',
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{y}<br>Type: %{x}<br>Net Value: %{z:$,.0f}<extra></extra>',
            ygap=2,  # Increased gap for better readability
            xgap=1,  # Add horizontal gap
            colorbar=dict(
                title=dict(text="Net Value ($)", side="right"),
                thickness=15,
                len=0.8,
                x=1.02
            )
        ))

        # CRITICAL FIX: Container-optimized Plotly Layout configuration
        fig.update_layout(
            title=dict(
                text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
                x=0.5,
                xanchor='center',
                font=dict(size=14)  # Slightly smaller for better container fit
            ),
            template=PLOTLY_TEMPLATE,
            # CRITICAL FIX: Container-aware margins to prevent cut-off
            margin=dict(l=60, r=80, t=50, b=40),  # Reduced margins for better container fit
            yaxis=dict(
                type='category',  # Essential for strike prices as categories
                title=dict(text="Strike Price", font=dict(size=11)),
                tickmode='linear',  # Use linear for better control
                autorange='reversed',  # Reverse to show higher strikes at top
                showgrid=True,
                gridcolor='rgba(128,128,128,0.3)',
                tickfont=dict(size=9),  # Smaller font for better fit
                side='left',
                automargin=False  # Disable automargin to use fixed margins
            ),
            xaxis=dict(
                title=dict(text="Option Type", font=dict(size=11)),
                tickfont=dict(size=10),
                showgrid=False,
                automargin=False  # Disable automargin to use fixed margins
            ),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            uirevision='constant',
            # CRITICAL FIX: Enhanced container-aware sizing - remove restrictive constraints
            autosize=True,  # Always use autosize for responsive behavior
            height=None     # Let container determine height naturally
        )
        
        apply_dark_theme_template(fig)

        # CRITICAL FIX: Enhanced ATM price line positioning for horizontal heatmaps
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            y_values_numeric = pivot_df.index.to_series().astype(float)
            if not y_values_numeric.empty:
                # Find the closest strike to current price
                closest_strike_val = y_values_numeric.iloc[(y_values_numeric - current_price).abs().argmin()]
                closest_strike_str = str(closest_strike_val)

                # CRITICAL FIX: Calculate proper position for price line in categorical y-axis
                # For categorical axes, we need to use the index position, not the value
                strike_categories = [str(strike) for strike in pivot_df.index.astype(str)]

                try:
                    # Find the index position of the closest strike in the categorical list
                    strike_position = strike_categories.index(closest_strike_str)

                    # Add horizontal line at the correct categorical position
                    fig.add_hline(
                        y=strike_position,  # Use index position for categorical axis
                        line=dict(
                            color="rgba(255, 255, 255, 0.8)",
                            width=2,
                            dash="dash"
                        ),
                        annotation_text=f"ATM ≈ ${current_price:.2f}",
                        annotation_position="right",
                        annotation=dict(
                            font=dict(color="white", size=10),
                            bgcolor="rgba(0, 0, 0, 0.5)",
                            bordercolor="white",
                            borderwidth=1
                        )
                    )

                except ValueError:
                    # Fallback: Add line using string representation if index method fails
                    fig.add_hline(
                        y=closest_strike_str,
                        line=dict(
                            color="rgba(255, 255, 255, 0.8)",
                            width=2,
                            dash="dash"
                        )
                    )

                    # Add separate annotation for fallback case
                    fig.add_annotation(
                        x=1.02,
                        y=closest_strike_str,
                        xref="paper",
                        yref="y",
                        text=f"ATM ≈ ${current_price:.2f}",
                        showarrow=False,
                        xanchor="left",
                        yanchor="middle",
                        font=dict(color="white", size=10),
                        bgcolor="rgba(0, 0, 0, 0.5)",
                        bordercolor="white",
                        borderwidth=1
                    )
            
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        # Use default_fig_height_for_empty for error figure
        fig = create_empty_figure(chart_name, default_fig_height_for_empty, f"Error: {e}")
        # CRITICAL FIX: Use Pydantic v2 compliant styling for error graph
        error_style, error_config = create_pydantic_graph_style(f'{default_fig_height_for_empty}px')
        graph_to_return = dcc.Graph(
            id=f"net-value-heatmap-{bundle.target_symbol}-error", # Different ID for error graph
            figure=fig,
            config=error_config,
            style=error_style
        )
        return _wrap_chart_in_card(graph_to_return, about_text, "net-value-heatmap")

    # CRITICAL FIX: Use centralized heatmap-specific styling that prevents cutoffs
    style_dict, config_dict = create_heatmap_style(
        calculated_height=dynamic_height if not autosize_enabled else None,
        enable_autosize=True  # Always enable autosize for responsive behavior
    )

    # Convert to Dash Graph with centralized styling
    success_graph = dcc.Graph(
        id=f"net-value-heatmap-{bundle.target_symbol}",
        figure=fig,
        config=config_dict,
        style=style_dict,
        className=CHART_CONTAINER_CLASSES
    )
    return _wrap_chart_in_card(success_graph, about_text, "net-value-heatmap")

def _generate_greek_flow_chart(bundle: FinalAnalysisBundleV2_5, metric: str, title: str, color: str) -> Component:
    """Generic helper to create a bar chart for a net customer Greek flow metric."""
    chart_name = f"{title} by Strike"
    about_text = (
        f"💡 {title} by Strike: Shows net customer {title.lower()} flow at each strike. "
        f"Blue/Green/Orange bars = net {title.lower()} buying/selling. "
        f"Use this to identify where the largest directional or volatility bets are being placed."
    )
    fig = go.Figure()
    
    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        if strike_data is None:
            logger.error(f"[{chart_name}] strike_level_data_with_metrics missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert(f"Strike data unavailable for {title}.", color="danger"), about_text, f"{title.lower()}-flow-chart")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        
        logger.info(f"[{chart_name}] Strike data shape: {df_strike.shape}")
        logger.info(f"[{chart_name}] Available columns: {df_strike.columns.tolist()}")
        logger.info(f"[{chart_name}] Looking for metric: {metric}")
        
        if not df_strike.empty:
            if metric in df_strike.columns:
                logger.info(f"[{chart_name}] Metric column sample values: {df_strike[metric].head(10).tolist()}")
                logger.info(f"[{chart_name}] Metric column null count: {df_strike[metric].isnull().sum()}")
                logger.info(f"[{chart_name}] Metric column non-null count: {df_strike[metric].notnull().sum()}")
                
                df_plot = df_strike.dropna(subset=['strike', metric]).sort_values('strike')
                logger.info(f"[{chart_name}] Plot data shape after filtering: {df_plot.shape}")
                
                # CRITICAL FIX: Zero-tolerance fake data compliance - NO fillna(0) allowed
                if df_plot.empty:
                    logger.error(f"[{chart_name}] No valid {metric} data available after filtering. Cannot display chart with missing financial data.")
                    fig = create_empty_figure(chart_name, 400, f"No valid {metric} data available - refusing to display fake data.")
                
                if not df_plot.empty:
                    non_zero_count = (df_plot[metric] != 0).sum()
                    logger.info(f"[{chart_name}] Non-zero values: {non_zero_count}")
                    
                    fig.add_trace(go.Bar(
                        x=df_plot['strike'],
                        y=df_plot[metric],
                        name=title,
                        marker_color=color,
                        hovertemplate=f'Strike: %{{x}}<br>{title}: %{{y:.4f}}<extra></extra>'
                    ))
                    fig.update_layout(
                        title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
                        height=400,
                        template=PLOTLY_TEMPLATE,
                        showlegend=False,
                        xaxis_title="Strike Price",
                        yaxis_title=title,
                        margin=dict(l=60, r=60, t=80, b=60)
                    )
                    apply_dark_theme_template(fig)
                    current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                    if current_price is not None:
                        add_price_line(fig, current_price)
                    add_timestamp_annotation(fig, bundle.bundle_timestamp)
                else:
                    logger.warning(f"[{chart_name}] No valid data after filtering")
                    fig = create_empty_figure(chart_name, 400, "No valid data after filtering.")
            else:
                logger.warning(f"[{chart_name}] Metric '{metric}' not found in columns: {df_strike.columns.tolist()}")
                fig = _try_calculate_greek_flow_fallback(df_strike, bundle, metric, title, color, chart_name)
        else:
            logger.warning(f"[{chart_name}] Strike data is empty")
            fig = create_empty_figure(chart_name, 400, "Strike data is empty.")
    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, 400, f"Error: {e}")

    # CRITICAL FIX: Use centralized bar chart styling for flow charts
    from dashboard_application.utils.chart_styling_utils_v2_5 import create_bar_chart_style
    flow_style, flow_config = create_bar_chart_style()
    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config=flow_config,
        style=flow_style,
        className=CHART_CONTAINER_CLASSES
    ), about_text, f"{title.lower()}-flow-chart")

def _generate_sgdhp_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates SGDHP (Strike-level Gamma Delta Hedging Pressure) heatmap."""
    chart_name = "SGDHP - Strike Gamma Delta Hedging Pressure"
    # CRITICAL FIX: Enhanced configuration access using proper Pydantic v2 model
    try:
        dashboard_config = config.config.visualization_settings.dashboard
        flow_settings = getattr(dashboard_config, 'flow_mode_settings', None)
        if flow_settings and hasattr(flow_settings, 'sgdhp_heatmap'):
            fig_height = flow_settings.sgdhp_heatmap.height
        else:
            fig_height = 500
    except (AttributeError, KeyError):
        fig_height = 500
    about_text = (
        "🧮 SGDHP Heatmap: Shows strike-level gamma-delta hedging pressure. "
        "High values = areas where dealers must hedge aggressively. "
        "Use this to spot potential volatility clusters and risk zones."
    )

    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if strike_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("SGDHP data unavailable.", color="danger"), about_text, "sgdhp-heatmap")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        df_options = pd.DataFrame([c.model_dump() for c in options_data])
        
        if df_strike.empty or 'sgdhp_score_strike' not in df_strike.columns:
            if not df_options.empty:
                current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                if current_price is not None:
                    sgdhp_data = _calculate_sgdhp_fallback(df_options, current_price)
                    if sgdhp_data is not None:
                        df_strike = sgdhp_data
                    else:
                        # CRITICAL FIX: Use Pydantic v2 compliant styling
                        error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                        return _wrap_chart_in_card(dcc.Graph(
                            figure=create_empty_figure(chart_name, fig_height, "SGDHP data not available."),
                            config=error_config,
                            style=error_style
                        ), about_text, "sgdhp-heatmap")
                else:
                    # CRITICAL FIX: Use Pydantic v2 compliant styling
                    error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                    return _wrap_chart_in_card(dcc.Graph(
                        figure=create_empty_figure(chart_name, fig_height, "Current price not available for SGDHP calculation."),
                        config=error_config,
                        style=error_style
                    ), about_text, "sgdhp-heatmap")
            else:
                # CRITICAL FIX: Use Pydantic v2 compliant styling
                error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, "No options data available for SGDHP calculation."),
                    config=error_config,
                    style=error_style
                ), about_text, "sgdhp-heatmap")

        df_plot = df_strike.dropna(subset=['strike', 'sgdhp_score_strike']).sort_values('strike')
        
        if df_plot.empty:
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid SGDHP data to display."),
                config=error_config,
                style=error_style
            ), about_text, "sgdhp-heatmap")

        strikes = df_plot['strike'].values
        sgdhp_values = np.array(df_plot['sgdhp_score_strike'].values)
        z_data = sgdhp_values.reshape(1, -1)
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=strikes,
            y=['SGDHP'],
            colorscale='RdYlGn_r',
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>SGDHP Score: %{z:.3f}<extra></extra>',
            colorbar=dict(title="SGDHP Score", x=1.02)
        ))
        
        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title=""
        )
        
        apply_dark_theme_template(fig)
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    # CRITICAL FIX: Use centralized heatmap-specific styling for SGDHP heatmap
    sgdhp_style, sgdhp_config = create_heatmap_style(enable_autosize=True)
    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config=sgdhp_config,
        style=sgdhp_style,
        className=CHART_CONTAINER_CLASSES
    ), about_text, "sgdhp-heatmap")

def _generate_ivsdh_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates IVSDH (Implied Volatility Surface Delta Hedging) heatmap."""
    chart_name = "IVSDH - IV Surface Delta Hedging"
    # CRITICAL FIX: Enhanced configuration access using proper Pydantic v2 model
    try:
        dashboard_config = config.config.visualization_settings.dashboard
        flow_settings = getattr(dashboard_config, 'flow_mode_settings', None)
        if flow_settings and hasattr(flow_settings, 'ivsdh_heatmap'):
            fig_height = flow_settings.ivsdh_heatmap.height
        else:
            fig_height = 500
    except (AttributeError, KeyError):
        fig_height = 500
    about_text = (
        "🌈 IVSDH Heatmap: Visualizes the implied volatility surface delta hedging. "
        "Shows how volatility and delta interact across strikes and expiries. "
        "Use this to identify where volatility is most sensitive to price moves."
    )

    try:
        und_data = getattr(bundle.processed_data_bundle, 'underlying_data_enriched', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if und_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("IVSDH data unavailable.", color="danger"), about_text, "ivsdh-heatmap")
        ivsdh_surface = getattr(und_data, 'ivsdh_surface_data', None)
        df_options = pd.DataFrame([c.model_dump() for c in options_data])
        
        if ivsdh_surface is None:
            if not df_options.empty:
                ivsdh_data = _calculate_ivsdh_fallback(df_options)
                if ivsdh_data is not None:
                    ivsdh_surface = ivsdh_data
                else:
                    # CRITICAL FIX: Use Pydantic v2 compliant styling
                    error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                    return _wrap_chart_in_card(dcc.Graph(
                        figure=create_empty_figure(chart_name, fig_height, "IVSDH surface data not available."),
                        config=error_config,
                        style=error_style
                    ), about_text, "ivsdh-heatmap")
            else:
                # CRITICAL FIX: Use Pydantic v2 compliant styling
                error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, "No options data available for IVSDH calculation."),
                    config=error_config,
                    style=error_style
                ), about_text, "ivsdh-heatmap")

        if not isinstance(ivsdh_surface, pd.DataFrame):
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "IVSDH surface data format not supported."),
                config=error_config,
                style=error_style
            ), about_text, "ivsdh-heatmap")

        if 'strike' in ivsdh_surface.columns and 'dte_calc' in ivsdh_surface.columns and 'ivsdh_score' in ivsdh_surface.columns:
            # CRITICAL FIX: Zero-tolerance fake data compliance - NO fillna(0) allowed
            pivot_df = ivsdh_surface.pivot_table(
                index='dte_calc',
                columns='strike',
                values='ivsdh_score',
                aggfunc='mean'
            )

            # FAIL-FAST: Check for missing volatility surface data
            if pivot_df.isna().any().any():
                nan_count = pivot_df.isna().sum().sum()
                logger.error(f"[{chart_name}] Found {nan_count} missing values in IVSDH surface data. Cannot display volatility surface with incomplete data.")
                # CRITICAL FIX: Use Pydantic v2 compliant styling
                error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, f"Incomplete IVSDH surface: {nan_count} missing volatility values"),
                    config=error_config,
                    style=error_style
                ), about_text, "ivsdh-heatmap")
        else:
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "Required columns not found in IVSDH surface data."),
                config=error_config,
                style=error_style
            ), about_text, "ivsdh-heatmap")

        if pivot_df.empty:
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid IVSDH surface data to display."),
                config=error_config,
                style=error_style
            ), about_text, "ivsdh-heatmap")

        fig = go.Figure(data=go.Heatmap(
            z=pivot_df.values,
            x=pivot_df.columns,
            y=pivot_df.index,
            colorscale='RdYlBu',
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>DTE: %{y}<br>IVSDH Score: %{z:.3f}<extra></extra>',
            colorbar=dict(title="IVSDH Score", x=1.02)
        ))
        
        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title="Days to Expiration"
        )
        
        apply_dark_theme_template(fig)
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    # CRITICAL FIX: Use centralized heatmap-specific styling for IVSDH heatmap
    ivsdh_style, ivsdh_config = create_heatmap_style(enable_autosize=True)
    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config=ivsdh_config,
        style=ivsdh_style,
        className=CHART_CONTAINER_CLASSES
    ), about_text, "ivsdh-heatmap")

def _generate_ugch_heatmap(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5) -> Component:
    """Generates UGCH (Unified Gamma Charm Hedging) heatmap."""
    chart_name = "UGCH - Unified Gamma Charm Hedging"
    # CRITICAL FIX: Enhanced configuration access using proper Pydantic v2 model
    try:
        dashboard_config = config.config.visualization_settings.dashboard
        flow_settings = getattr(dashboard_config, 'flow_mode_settings', None)
        if flow_settings and hasattr(flow_settings, 'ugch_heatmap'):
            fig_height = flow_settings.ugch_heatmap.height
        else:
            fig_height = 500
    except (AttributeError, KeyError):
        fig_height = 500
    about_text = (
        "🟣 UGCH Heatmap: Shows unified gamma-charm hedging intensity. "
        "Highlights where both gamma and charm are driving dealer hedging. "
        "Use this to spot high-risk, high-volatility strike zones."
    )

    try:
        strike_data = getattr(bundle.processed_data_bundle, 'strike_level_data_with_metrics', None)
        options_data = getattr(bundle.processed_data_bundle, 'options_data_with_metrics', None)
        if strike_data is None or options_data is None:
            logger.error(f"[{chart_name}] Required data missing from bundle.")
            return _wrap_chart_in_card(dbc.Alert("UGCH data unavailable.", color="danger"), about_text, "ugch-heatmap")
        df_strike = pd.DataFrame([s.model_dump() for s in strike_data])
        df_options = pd.DataFrame([c.model_dump() for c in options_data])
        
        if df_strike.empty or 'ugch_score_strike' not in df_strike.columns:
            if not df_options.empty:
                current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                if current_price is not None:
                    ugch_data = _calculate_ugch_fallback(df_options, current_price)
                    if ugch_data is not None:
                        df_strike = ugch_data
                    else:
                        # CRITICAL FIX: Use Pydantic v2 compliant styling
                        error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                        return _wrap_chart_in_card(dcc.Graph(
                            figure=create_empty_figure(chart_name, fig_height, "UGCH data not available."),
                            config=error_config,
                            style=error_style
                        ), about_text, "ugch-heatmap")
                else:
                    # CRITICAL FIX: Use Pydantic v2 compliant styling
                    error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                    return _wrap_chart_in_card(dcc.Graph(
                        figure=create_empty_figure(chart_name, fig_height, "Current price not available for UGCH calculation."),
                        config=error_config,
                        style=error_style
                    ), about_text, "ugch-heatmap")
            else:
                # CRITICAL FIX: Use Pydantic v2 compliant styling
                error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
                return _wrap_chart_in_card(dcc.Graph(
                    figure=create_empty_figure(chart_name, fig_height, "No options data available for UGCH calculation."),
                    config=error_config,
                    style=error_style
                ), about_text, "ugch-heatmap")

        df_plot = df_strike.dropna(subset=['strike', 'ugch_score_strike']).sort_values('strike')
        
        if df_plot.empty:
            # CRITICAL FIX: Use Pydantic v2 compliant styling
            error_style, error_config = create_pydantic_graph_style(f'{fig_height}px')
            return _wrap_chart_in_card(dcc.Graph(
                figure=create_empty_figure(chart_name, fig_height, "No valid UGCH data to display."),
                config=error_config,
                style=error_style
            ), about_text, "ugch-heatmap")

        strikes = df_plot['strike'].values
        ugch_values = df_plot['ugch_score_strike'].values
        intensity_bands = ['Low', 'Medium', 'High']
        z_data = np.tile(np.array(ugch_values), (len(intensity_bands), 1))
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=strikes,
            y=intensity_bands,
            colorscale='Viridis',
            zmid=0,
            hoverongaps=False,
            hovertemplate='Strike: %{x}<br>Intensity: %{y}<br>UGCH Score: %{z:.3f}<extra></extra>',
            colorbar=dict(title="UGCH Score", x=1.02)
        ))
        
        fig.update_layout(
            title_text=f"<b>{bundle.target_symbol}</b> - {chart_name}",
            height=fig_height,
            template=PLOTLY_TEMPLATE,
            xaxis_title="Strike Price",
            yaxis_title="Hedging Intensity"
        )
        
        apply_dark_theme_template(fig)
        current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
        if current_price is not None:
            add_price_line(fig, current_price)
        add_timestamp_annotation(fig, bundle.bundle_timestamp)

    except Exception as e:
        logger.error(f"Error creating {chart_name}: {e}", exc_info=True)
        fig = create_empty_figure(chart_name, fig_height, f"Error: {e}")

    # CRITICAL FIX: Use centralized heatmap-specific styling for UGCH heatmap
    ugch_style, ugch_config = create_heatmap_style(enable_autosize=True)
    return _wrap_chart_in_card(dcc.Graph(
        figure=fig,
        config=ugch_config,
        style=ugch_style,
        className=CHART_CONTAINER_CLASSES
    ), about_text, "ugch-heatmap")

# --- Fallback Calculation Functions ---

def _calculate_sgdhp_fallback(df_options: pd.DataFrame, underlying_price: float) -> Optional[pd.DataFrame]:
    """Fallback calculation for SGDHP when not available in processed data."""
    try:
        if 'strike' not in df_options.columns or 'gxoi' not in df_options.columns or 'dxoi' not in df_options.columns:
            return None
            
        strike_groups = df_options.groupby('strike').agg({
            'gxoi': 'sum',
            'dxoi': 'sum',
            'open_interest': 'sum'
        }).reset_index()
        
        strike_groups['distance_from_atm'] = abs(strike_groups['strike'] - underlying_price) / underlying_price
        
        strike_groups['sgdhp_score_strike'] = (
            strike_groups['gxoi'] * strike_groups['dxoi'] * 
            np.exp(-strike_groups['distance_from_atm'] * 2)
        ) / (strike_groups['open_interest'] + 1)
        
        return strike_groups[['strike', 'sgdhp_score_strike']]
        
    except Exception as e:
        logger.error(f"Error in SGDHP fallback calculation: {e}")
        return None

def _calculate_ivsdh_fallback(df_options: pd.DataFrame) -> Optional[pd.DataFrame]:
    """Fallback calculation for IVSDH when not available in processed data."""
    try:
        required_cols = ['strike', 'dte_calc', 'iv', 'delta_contract', 'vega_contract']
        if not all(col in df_options.columns for col in required_cols):
            return None
            
        df_calc = df_options.copy()
        df_calc['ivsdh_score'] = (
            df_calc['iv'] * abs(df_calc['delta_contract']) * df_calc['vega_contract']
        ) / (df_calc['dte_calc'] + 1)
        
        return df_calc[['strike', 'dte_calc', 'ivsdh_score']]
        
    except Exception as e:
        logger.error(f"Error in IVSDH fallback calculation: {e}")
        return None

def _calculate_ugch_fallback(df_options: pd.DataFrame, underlying_price: float) -> Optional[pd.DataFrame]:
    """Fallback calculation for UGCH when not available in processed data."""
    try:
        required_cols = ['strike', 'gamma_contract', 'charm_contract']
        if not all(col in df_options.columns for col in required_cols):
            return None
            
        strike_groups = df_options.groupby('strike').agg({
            'gamma_contract': 'sum',
            'charm_contract': 'sum'
        }).reset_index()
        
        strike_groups['ugch_score_strike'] = (
            abs(strike_groups['gamma_contract']) * abs(strike_groups['charm_contract'])
        )
        
        return strike_groups[['strike', 'ugch_score_strike']]
        
    except Exception as e:
        logger.error(f"Error in UGCH fallback calculation: {e}")
        return None


def _try_calculate_greek_flow_fallback(df_strike: pd.DataFrame, bundle: FinalAnalysisBundleV2_5, metric: str, title: str, color: str, chart_name: str) -> go.Figure:
    """Try to calculate Greek flow metrics from available data when they're missing."""
    try:
        logger.info(f"[{chart_name}] Attempting fallback calculation for {metric}")
        
        metric_calculations = {
            'net_customer_delta_flow': lambda df: _calculate_net_flow(df, 'delta_contract', 'customer'),
            'net_customer_gamma_flow': lambda df: _calculate_net_flow(df, 'gamma_contract', 'customer'),
            'net_customer_vega_flow': lambda df: _calculate_net_flow(df, 'vega_contract', 'customer'),
            'net_customer_theta_flow': lambda df: _calculate_net_flow(df, 'theta_contract', 'customer'),
            'net_customer_charm_flow': lambda df: _calculate_net_flow(df, 'charm_contract', 'customer')
        }
        
        if metric in metric_calculations:
            df_calc = metric_calculations[metric](df_strike)
            
            if not df_calc.empty and metric in df_calc.columns:
                logger.info(f"[{chart_name}] Successfully calculated {metric} using fallback")
                
                fig = go.Figure()
                df_plot = df_calc.dropna(subset=['strike', metric]).sort_values('strike')
                
                if not df_plot.empty:
                    fig.add_trace(go.Bar(
                        x=df_plot['strike'],
                        y=df_plot[metric],
                        name=title,
                        marker_color=color,
                        hovertemplate=f'Strike: %{{x}}<br>{title}: %{{y:.4f}}<extra></extra>'
                    ))
                    fig.update_layout(
                        title_text=f"<b>{bundle.target_symbol}</b> - {chart_name} (Calculated)",
                        height=400,
                        template=PLOTLY_TEMPLATE,
                        showlegend=False,
                        xaxis_title="Strike Price",
                        yaxis_title=title,
                        margin=dict(l=60, r=60, t=80, b=60)
                    )
                    apply_dark_theme_template(fig)
                    current_price = getattr(bundle.processed_data_bundle.underlying_data_enriched, 'price', None)
                    if current_price is not None:
                        add_price_line(fig, current_price)
                    add_timestamp_annotation(fig, bundle.bundle_timestamp)
                    return fig
                else:
                    logger.warning(f"[{chart_name}] Calculated data is empty after filtering")
            else:
                logger.warning(f"[{chart_name}] Failed to calculate {metric} using fallback")
        else:
            logger.warning(f"[{chart_name}] No fallback calculation available for {metric}")
        
        return create_empty_figure(chart_name, 400, f"Metric '{metric}' not available and cannot be calculated.")
        
    except Exception as e:
        logger.error(f"Error in Greek flow fallback calculation for {metric}: {e}")
        return create_empty_figure(chart_name, 400, f"Error calculating {metric}: {e}")


def _calculate_net_flow(df_strike: pd.DataFrame, greek_col: str, flow_type: str) -> pd.DataFrame:
    """Calculate net flow for a specific Greek and flow type."""
    try:
        required_cols = ['strike', greek_col, 'open_interest', 'volume']
        available_cols = [col for col in required_cols if col in df_strike.columns]
        
        if len(available_cols) < 2:
            logger.warning(f"Insufficient columns for {greek_col} flow calculation. Available: {available_cols}")
            return pd.DataFrame()
        
        df_calc = df_strike[available_cols].copy()
        
        # CRITICAL FIX: Zero-tolerance fake data compliance - NO synthetic flow calculations
        if greek_col in df_calc.columns:
            if 'volume' in df_calc.columns:
                # Only use real volume data for flow calculations
                df_calc[f'net_{flow_type}_{greek_col.replace("_contract", "")}_flow'] = df_calc[greek_col] * df_calc['volume']
                return df_calc
            else:
                # FAIL-FAST: Cannot calculate flow without real volume data
                logger.error(f"Cannot calculate {flow_type} {greek_col} flow: volume data required but not available. Refusing to synthesize flow data.")
                return pd.DataFrame()

        else:
            logger.error(f"Greek column {greek_col} not found in data")
            return pd.DataFrame()
            
    except Exception as e:
        logger.error(f"Error calculating net flow for {greek_col}: {e}")
        return pd.DataFrame()

# --- Main Layout Function ---

def create_layout(bundle: FinalAnalysisBundleV2_5, config: ConfigManagerV2_5, control_panel_state=None) -> html.Div:
    """
    Creates the complete layout for the "Flow Breakdown" mode.
    Enforces Pydantic validation at the UI boundary.
    Now respects control panel settings for DTE range, price range %, and refresh interval.
    """
    if not isinstance(bundle, FinalAnalysisBundleV2_5):
        raise ValueError("Input bundle is not a FinalAnalysisBundleV2_5 Pydantic model.")
    if not bundle or not bundle.processed_data_bundle:
        return html.Div([
            dbc.Alert("Flow data is not available. Cannot render Flow Mode.", color="danger")
        ])
    if not hasattr(bundle.processed_data_bundle, 'underlying_data_enriched') or not bundle.processed_data_bundle.underlying_data_enriched:
        return html.Div([
            dbc.Alert("Underlying data is not available. Cannot render Flow Mode.", color="danger")
        ])

    # Log control panel state usage for debugging
    if control_panel_state:
        logger.info(f"Flow Mode using control panel state: DTE {control_panel_state.dte_min}-{control_panel_state.dte_max}, "
                   f"Price Range ±{control_panel_state.price_range_percent}%, "
                   f"Refresh {control_panel_state.refresh_interval_seconds}s")
        # Store control panel state for use in chart generators
        # Note: In a full implementation, chart generators would be updated to accept control_panel_state parameter
    else:
        logger.warning("Flow Mode: No control panel state available, using config defaults")

    try:
        net_value_heatmap = _generate_net_value_heatmap(bundle, config)
        delta_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_delta_flow_at_strike', 'Delta Flow', '#4A9EFF')
        gamma_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_gamma_flow_at_strike', 'Gamma Flow', '#10B981')
        vega_flow_chart = _generate_greek_flow_chart(bundle, 'net_cust_vega_flow_at_strike', 'Vega Flow', '#FFB84A')
        
        sgdhp_heatmap = _generate_sgdhp_heatmap(bundle, config)
        ivsdh_heatmap = _generate_ivsdh_heatmap(bundle, config)
        ugch_heatmap = _generate_ugch_heatmap(bundle, config)
        
        layout = html.Div([
            dbc.Row([
                dbc.Col(net_value_heatmap, width=12)
            ], className="mb-4"),
            
            dbc.Row([
                dbc.Col(delta_flow_chart, width=4),
                dbc.Col(gamma_flow_chart, width=4),
                dbc.Col(vega_flow_chart, width=4)
            ], className="mb-3"),
            
            dbc.Row([
                dbc.Col(sgdhp_heatmap, width=12)
            ], className="mb-3"),
            
            dbc.Row([
                dbc.Col(ivsdh_heatmap, width=6),
                dbc.Col(ugch_heatmap, width=6)
            ], className="mb-3")
        ])
        
        return layout
        
    except Exception as e:
        logger.error(f"Error creating flow mode layout: {e}", exc_info=True)
        return html.Div([
            dbc.Alert(
                f"Error loading flow mode: {str(e)}",
                color="danger",
                className="m-3"
            )
        ])